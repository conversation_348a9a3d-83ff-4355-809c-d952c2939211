import type { Metadata } from "next";
import { Source_Serif_4 } from "next/font/google";
import "./globals.css";
import CookieConsent from "../components/ui/CookieConsent";

const sourceSerif = Source_Serif_4({
  subsets: ["latin"],
  variable: "--font-source-serif",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Ottica GR1 - Occhiali da Vista, da Sole e Lenti a Contatto",
  description: "Ottica GR1 a Montesacro dal 1982. Occhiali da vista, da sole, lenti a contatto e controllo vista. Tradizione, qualità e innovazione per la tua salute visiva.",
  keywords: "ottica, occhiali da vista, occhiali da sole, lenti a contatto, controllo vista, Montesacro, Roma",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="it" className={sourceSerif.variable}>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className="font-serif antialiased">
        {children}
        <CookieConsent />
      </body>
    </html>
  );
}
