'use client';

import { useEffect, useState } from 'react';
import Carousel from '../ui/Carousel';

const BrandsSection = () => {
  const [brands, setBrands] = useState<Array<{id: string, src: string, alt: string}>>([]);

  useEffect(() => {
    // Real brand images from the available files
    const brandImages = [
      { id: '1', src: '/images/marchi/occhiali/rayban-logo.jpg', alt: 'Ray-Ban' },
      { id: '2', src: '/images/marchi/occhiali/persol-logo.jpg', alt: 'Persol' },
      { id: '3', src: '/images/marchi/occhiali/vogue-logo.jpg', alt: 'Vogue' },
      { id: '4', src: '/images/marchi/occhiali/Karl<PERSON>agerfeld-logo.jpg', alt: '<PERSON>' },
      { id: '5', src: '/images/marchi/occhiali/Dsquared2-logo.jpg', alt: 'Dsquared2' },
      { id: '6', src: '/images/marchi/occhiali/Mark<PERSON>-logo.jpg', alt: '<PERSON>' },
    ];
    setBrands(brandImages);
  }, []);

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-4">
            I Migliori Marchi
          </h2>
          <p className="text-lg text-text-base opacity-80 max-w-2xl mx-auto leading-relaxed">
            Collaboriamo con i brand più prestigiosi del settore ottico per offrirti 
            sempre il meglio in termini di qualità, design e innovazione.
          </p>
        </div>

        {/* Brands Carousel */}
        {brands.length > 0 && (
          <div className="max-w-6xl mx-auto">
            <Carousel
              items={brands}
              autoPlay={true}
              autoPlayInterval={3500}
              showDots={false}
              showArrows={true}
              itemsPerView={4}
              className="brands-carousel"
            />
          </div>
        )}

        {/* Additional Info */}
        <div className="text-center mt-12">
          <p className="text-text-base opacity-70 max-w-3xl mx-auto">
            Ogni marchio è selezionato con cura per garantire ai nostri clienti 
            prodotti di eccellenza che uniscono funzionalità, comfort e stile. 
            Scopri la nostra collezione completa visitando il nostro negozio.
          </p>
        </div>
      </div>

      <style jsx>{`
        .brands-carousel :global(.carousel-item) {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 1rem;
        }
        
        @media (max-width: 768px) {
          .brands-carousel {
            --items-per-view: 2;
          }
        }
        
        @media (max-width: 480px) {
          .brands-carousel {
            --items-per-view: 1;
          }
        }
      `}</style>
    </section>
  );
};

export default BrandsSection;
