'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const isHomepage = pathname === '/';

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navLinks = [
    { href: '/', label: 'HOME' },
    { href: '/storia', label: 'STORIA' },
    { href: '/servizi', label: 'SERVIZI' },
    { href: '/occhiali', label: 'OCCHIALI' },
    { href: '/lenti-contatto', label: 'LENTI A CONTATTO' },
    { href: '/occhiali-sole', label: 'OCCHIALI DA SOLE' },
    { href: '/esami-vista', label: 'ESAMI DELLA VISTA' },
    { href: '/contatti', label: 'CONTATTI' },
  ];

  // Determine header background based on page and scroll state
  const getHeaderBackground = () => {
    if (isScrolled) {
      return 'bg-white shadow-lg';
    }
    if (isHomepage) {
      return 'bg-white/10 backdrop-blur-md';
    }
    return 'bg-white/90 backdrop-blur-sm shadow-sm';
  };

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${getHeaderBackground()}`}
    >
      <nav className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3">
            <div className="relative h-10 w-10 flex-shrink-0">
              <Image
                src="/images/logo/logo3.png"
                alt="Ottica GR1 Logo"
                fill
                className="object-contain"
                priority
              />
            </div>
            <div className="text-2xl font-bold font-sans">
              <span className={`${isScrolled || !isHomepage ? 'text-primary' : 'text-white'} transition-colors duration-300`}>
                OTTICA
              </span>
              <span className={`${isScrolled || !isHomepage ? 'text-accent' : 'text-white'} transition-colors duration-300 ml-1`}>
                GR1
              </span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`font-sans text-sm font-medium transition-colors duration-300 hover:text-accent ${
                  isScrolled || !isHomepage
                    ? pathname === link.href
                      ? 'text-primary'
                      : 'text-text-base hover:text-primary'
                    : pathname === link.href
                    ? 'text-accent'
                    : 'text-white hover:text-accent'
                }`}
              >
                {link.label}
              </Link>
            ))}
          </div>

          {/* Mobile Menu Button */}
          <button
            className="lg:hidden flex flex-col space-y-1 w-6 h-6"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle mobile menu"
          >
            <span
              className={`block h-0.5 w-6 transition-all duration-300 ${
                isScrolled || !isHomepage ? 'bg-primary' : 'bg-white'
              } ${isMobileMenuOpen ? 'rotate-45 translate-y-1.5' : ''}`}
            />
            <span
              className={`block h-0.5 w-6 transition-all duration-300 ${
                isScrolled || !isHomepage ? 'bg-primary' : 'bg-white'
              } ${isMobileMenuOpen ? 'opacity-0' : ''}`}
            />
            <span
              className={`block h-0.5 w-6 transition-all duration-300 ${
                isScrolled || !isHomepage ? 'bg-primary' : 'bg-white'
              } ${isMobileMenuOpen ? '-rotate-45 -translate-y-1.5' : ''}`}
            />
          </button>
        </div>

        {/* Mobile Navigation */}
        <div
          className={`lg:hidden overflow-hidden transition-all duration-300 ${
            isMobileMenuOpen ? 'max-h-96 mt-4' : 'max-h-0'
          }`}
        >
          <div className="bg-white rounded-lg shadow-lg p-4 space-y-3">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`block font-sans text-sm font-medium transition-colors duration-300 py-2 px-3 rounded ${
                  pathname === link.href
                    ? 'text-primary bg-primary/10'
                    : 'text-text-base hover:text-primary hover:bg-primary/5'
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {link.label}
              </Link>
            ))}
          </div>
        </div>
      </nav>
    </header>
  );
};

export default Header;
