'use client';

import Card from '../ui/Card';
import Button from '../ui/Button';

const ServicesSection = () => {
  const services = [
    {
      id: 'occhiali-vista',
      title: 'Occhiali da Vista',
      description: 'Soluzioni personalizzate per ogni esigenza visiva con le migliori marche del settore.',
      image: '/images/section-cards/DSC09553.jpeg',
      link: '/occhiali',
    },
    {
      id: 'occhiali-sole',
      title: 'Occhiali da Sole',
      description: 'Protezione e stile con una vasta selezione di occhiali da sole di alta qualità.',
      image: '/images/section-cards/DSC09556.jpeg',
      link: '/occhiali-sole',
    },
    {
      id: 'lenti-contatto',
      title: '<PERSON><PERSON> a Contatto',
      description: 'Comfort e libertà di movimento con lenti a contatto di ultima generazione.',
      image: '/images/section-cards/DSC09603.jpeg',
      link: '/lenti-contatto',
    },
  ];

  return (
    <section className="py-16 bg-background">
      <div className="container mx-auto px-4">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-4">
            I Nostri Servizi
          </h2>
          <p className="text-lg text-text-base opacity-80 max-w-2xl mx-auto leading-relaxed">
            Da oltre 40 anni offriamo soluzioni complete per la tua salute visiva, 
            combinando tradizione artigianale e tecnologie all'avanguardia.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {services.map((service) => (
            <Card
              key={service.id}
              image={service.image}
              imageAlt={service.title}
              title={service.title}
              description={service.description}
              grayscaleHover={true}
              className="h-full"
            >
              <Button
                variant="outline"
                onClick={() => window.location.href = service.link}
                className="w-full"
              >
                Scopri di più
              </Button>
            </Card>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="bg-white rounded-2xl shadow-lg p-8 md:p-12 max-w-4xl mx-auto">
            <h3 className="text-2xl md:text-3xl font-bold font-sans text-text-base mb-4">
              Controllo Vista Professionale
            </h3>
            <p className="text-lg text-text-base opacity-80 mb-6 leading-relaxed">
              Prenota un controllo della vista con i nostri specialisti. 
              Utilizziamo tecnologie diagnostiche avanzate per garantire la massima precisione.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="primary"
                size="lg"
                onClick={() => window.location.href = '/esami-vista'}
              >
                Prenota Controllo Vista
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => window.location.href = '/contatti'}
              >
                Contattaci
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
