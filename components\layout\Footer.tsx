import Link from 'next/link';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-primary text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Logo e Descrizione */}
          <div className="space-y-4">
            <div className="text-2xl font-bold font-sans">
              <span className="text-white">OTTICA</span>
              <span className="text-accent ml-1">GR1</span>
            </div>
            <p className="text-sm leading-relaxed opacity-90">
              Dal 1982 a Montesacro, tradizione e innovazione per la tua salute visiva. 
              Occhiali da vista, da sole, lenti a contatto e controllo vista.
            </p>
          </div>

          {/* Link Rapidi */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold font-sans">Link Rapidi</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/storia" className="text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300">
                  La Nostra Storia
                </Link>
              </li>
              <li>
                <Link href="/servizi" className="text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300">
                  I Nostri Servizi
                </Link>
              </li>
              <li>
                <Link href="/occhiali" className="text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300">
                  Occhiali da Vista
                </Link>
              </li>
              <li>
                <Link href="/occhiali-sole" className="text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300">
                  Occhiali da Sole
                </Link>
              </li>
              <li>
                <Link href="/lenti-contatto" className="text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300">
                  Lenti a Contatto
                </Link>
              </li>
            </ul>
          </div>

          {/* Servizi */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold font-sans">Servizi</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/esami-vista" className="text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300">
                  Esami della Vista
                </Link>
              </li>
              <li>
                <span className="text-sm opacity-90">Consulenza Personalizzata</span>
              </li>
              <li>
                <span className="text-sm opacity-90">Riparazioni</span>
              </li>
              <li>
                <span className="text-sm opacity-90">Assistenza Post-Vendita</span>
              </li>
            </ul>
          </div>

          {/* Contatti */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold font-sans">Contatti</h3>
            <div className="space-y-2 text-sm opacity-90">
              <p>📍 Via Montesacro, Roma</p>
              <p>📞 06 123 456 789</p>
              <p>✉️ <EMAIL></p>
              <div className="pt-2">
                <p className="font-medium">Orari di Apertura:</p>
                <p>Lun-Ven: 9:00 - 19:30</p>
                <p>Sab: 9:00 - 13:00</p>
                <p>Dom: Chiuso</p>
              </div>
            </div>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-primary-light mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-sm opacity-75">
              © {currentYear} Ottica GR1. Tutti i diritti riservati.
            </p>
            <div className="flex space-x-6">
              <Link href="/privacy" className="text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300">
                Privacy Policy
              </Link>
              <Link href="/cookie" className="text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300">
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
