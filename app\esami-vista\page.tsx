'use client';

import Header from '../../components/layout/Header';
import Footer from '../../components/layout/Footer';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import Image from 'next/image';

export default function EsamiVistaPage() {
  const examTypes = [
    {
      title: 'Controllo Vista Completo',
      description: 'Esame approfondito della vista con misurazione della refrazione e controllo della salute oculare.',
      duration: '30-45 minuti',
      price: 'Gratuito*',
      features: ['Misurazione vista', 'Controllo refrazione', 'Analisi binoculare', 'Consulenza personalizzata']
    },
    {
      title: 'Esame Computerizzato',
      description: 'Tecnologia avanzata per una misurazione precisa e rapida dei difetti visivi.',
      duration: '20-30 minuti',
      price: 'Incluso',
      features: ['Autorefractometro', 'Topografia corneale', 'Analisi digitale', 'Report dettagliato']
    },
    {
      title: 'Controllo Lenti a Contatto',
      description: 'Valutazione specifica per l\'applicazione e il controllo delle lenti a contatto.',
      duration: '45-60 minuti',
      price: 'Su richiesta',
      features: ['Test di compatibilità', 'Prova lenti', 'Istruzioni d\'uso', 'Follow-up incluso']
    }
  ];

  const technologies = [
    {
      name: 'Autorefractometro Digitale',
      description: 'Misurazione automatica e precisa dei difetti refrattivi',
      icon: '🔬'
    },
    {
      name: 'Topografo Corneale',
      description: 'Mappatura dettagliata della superficie corneale',
      icon: '🗺️'
    },
    {
      name: 'Forottero Computerizzato',
      description: 'Determinazione precisa della correzione ottimale',
      icon: '💻'
    },
    {
      name: 'Biomicroscopio',
      description: 'Esame dettagliato delle strutture oculari anteriori',
      icon: '🔍'
    }
  ];

  return (
    <div className="min-h-screen">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-16 bg-gradient-to-b from-primary/5 to-background">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <p className="text-accent text-lg font-sans font-medium mb-4">
                  Tecnologie Avanzate
                </p>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold font-sans text-text-base mb-6">
                  Esami della Vista
                </h1>
                <p className="text-xl text-text-base opacity-80 leading-relaxed mb-8">
                  Prenota un controllo della vista professionale con le nostre tecnologie diagnostiche 
                  di ultima generazione. I nostri esperti ti offriranno un servizio completo e personalizzato.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={() => window.location.href = '/contatti'}
                  >
                    Prenota Ora
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={() => window.location.href = 'tel:06123456789'}
                  >
                    Chiama Ora
                  </Button>
                </div>
              </div>
              <div className="relative h-96 lg:h-[500px] rounded-lg overflow-hidden shadow-xl">
                <Image
                  src="/images/slider/DSC09552.jpeg"
                  alt="Controllo vista professionale"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Exam Types Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-4">
                I Nostri Servizi
              </h2>
              <p className="text-lg text-text-base opacity-80 max-w-2xl mx-auto">
                Offriamo diversi tipi di controlli per soddisfare ogni esigenza
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {examTypes.map((exam, index) => (
                <Card key={index} className="h-full">
                  <div className="p-6">
                    <h3 className="text-xl font-semibold font-sans text-text-base mb-3">
                      {exam.title}
                    </h3>
                    <p className="text-text-base opacity-80 mb-4">
                      {exam.description}
                    </p>
                    
                    <div className="flex justify-between items-center mb-4 text-sm">
                      <span className="text-accent font-medium">Durata: {exam.duration}</span>
                      <span className="text-primary font-bold">{exam.price}</span>
                    </div>
                    
                    <ul className="space-y-2 mb-6">
                      {exam.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-sm text-text-base">
                          <span className="text-accent mr-2">✓</span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                    
                    <Button
                      variant="outline"
                      fullWidth
                      onClick={() => window.location.href = '/contatti'}
                    >
                      Prenota
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
            
            <div className="text-center mt-8">
              <p className="text-sm text-text-base opacity-70">
                * Gratuito con l'acquisto di occhiali o lenti a contatto
              </p>
            </div>
          </div>
        </section>

        {/* Technologies Section */}
        <section className="py-16 bg-background">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-4">
                Tecnologie All'Avanguardia
              </h2>
              <p className="text-lg text-text-base opacity-80 max-w-2xl mx-auto">
                Utilizziamo strumentazione professionale di ultima generazione per garantire 
                la massima precisione nei nostri controlli
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {technologies.map((tech, index) => (
                <div key={index} className="text-center p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                  <div className="text-4xl mb-4">{tech.icon}</div>
                  <h3 className="text-lg font-semibold font-sans text-text-base mb-3">
                    {tech.name}
                  </h3>
                  <p className="text-text-base opacity-80 text-sm">
                    {tech.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Process Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-4">
                Come Funziona
              </h2>
              <p className="text-lg text-text-base opacity-80 max-w-2xl mx-auto">
                Il nostro processo di controllo vista in 4 semplici passaggi
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                { step: '1', title: 'Prenotazione', desc: 'Prenota il tuo appuntamento online o per telefono' },
                { step: '2', title: 'Accoglienza', desc: 'Ti accogliamo e raccogliamo la tua storia visiva' },
                { step: '3', title: 'Esame', desc: 'Eseguiamo il controllo con strumentazione avanzata' },
                { step: '4', title: 'Consulenza', desc: 'Ti consigliamo la soluzione migliore per te' }
              ].map((item, index) => (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-2xl font-bold font-sans mx-auto mb-4">
                    {item.step}
                  </div>
                  <h3 className="text-xl font-semibold font-sans text-text-base mb-3">
                    {item.title}
                  </h3>
                  <p className="text-text-base opacity-80">
                    {item.desc}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-primary text-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-4xl font-bold font-sans mb-6">
              Prenota il Tuo Controllo Vista
            </h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Non aspettare, la tua vista è importante. Prenota oggi stesso un controllo 
              professionale con i nostri esperti.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="accent"
                size="lg"
                onClick={() => window.location.href = '/contatti'}
              >
                Prenota Online
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => window.location.href = 'tel:06123456789'}
                className="border-white text-white hover:bg-white hover:text-primary"
              >
                Chiama: 06 123 456 789
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
}
